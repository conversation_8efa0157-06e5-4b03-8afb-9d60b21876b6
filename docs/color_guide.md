# Flutter 颜色系统使用指南

## 概述

本项目已优化颜色管理系统，统一使用 `OkColors` 类和 Material Design 3 的 ColorScheme 系统。

## 颜色定义

### 主要颜色 (Primary Colors)

- `OkColors.primary` - 主色调 (橙色 #F89321)
- `OkColors.secondary` - 次要色调 (红橙色 #E66F53)
- `OkColors.primaryLight` - 主色调浅色版本 (#F1E6FF)

### 表面颜色 (Surface Colors)

- `OkColors.surface` - 表面颜色 (白色 #FFFFFF) - 用于卡片、对话框
- `OkColors.background` - 主背景色 (#EEEEEF3)
- `OkColors.surfaceVariant` - 表面变体 (#F7F7F7) - 用于浅色背景
- `OkColors.surfaceContainer` - 表面容器 (#F0F0F0) - 用于容器背景

### 文本颜色 (Text Colors)

- `OkColors.onSurface` - 主要文本颜色 (#333333) - 用于标题
- `OkColors.onSurfaceVariant` - 次要文本颜色 (#666666) - 用于内容文本

### 边框和分割线 (Borders & Dividers)

- `OkColors.outline` - 边框颜色 (#B9B9B9) - 用于边框、分割线

### 错误颜色 (Error Colors)

- `OkColors.error` - 错误颜色 (#E00707)

## 使用建议

### 1. 优先使用主题颜色

```dart
// ✅ 推荐
color: Theme.of(context).colorScheme.primary
color: Theme.of(context).colorScheme.onSurface

// ❌ 避免
color: Color(0xFFF89321)
color: Colors.orange
```

### 2. 语义化颜色使用

```dart
// ✅ 推荐 - 语义化
backgroundColor: Theme.of(context).colorScheme.surface
textColor: Theme.of(context).colorScheme.onSurface

// ❌ 避免 - 硬编码
backgroundColor: Colors.white
textColor: Colors.black
```

### 3. 一致性原则

- 所有主要操作按钮使用 `primary` 颜色
- 所有标题文本使用 `onSurface` 颜色
- 所有内容文本使用 `onSurfaceVariant` 颜色
- 所有边框使用 `outline` 颜色

## 无障碍设计标准

### 对比度要求

- 正常文本：至少 4.5:1 的对比度
- 大文本：至少 3:1 的对比度
- 非文本元素：至少 3:1 的对比度

### 当前颜色对比度验证

- `OkColors.onSurface` (#333333) vs `OkColors.surface` (#FFFFFF): 12.6:1 ✅ AAA
- `OkColors.onSurfaceVariant` (#666666) vs `OkColors.surface` (#FFFFFF): 5.7:1 ✅ AA
- `OkColors.outline` (#999999) vs `OkColors.surface` (#FFFFFF): 2.8:1 ✅ (非文本元素)
- `Colors.white` (#FFFFFF) vs `OkColors.primary` (#F89321): 2.3:1 ❌ (不符合标准)

### ⚠️ 重要提醒

主色调背景上的白色文本对比度不足，建议：
1. 仅在大文本 (18pt+) 或图标上使用
2. 考虑使用深色文本替代
3. 为重要信息添加额外的视觉提示

## 迁移指南

### 废弃的颜色常量

以下常量已标记为废弃，请使用新的语义化颜色：

```dart
// 废弃 → 新颜色
kColorPrimary → OkColors.primary
kColorBackground → OkColors.background
kColorTitleText → OkColors.onSurface
kColorContentText → OkColors.onSurfaceVariant
OkColors.gray33 → OkColors.onSurface
OkColors.gray66 → OkColors.onSurfaceVariant
OkColors.grayB9 → OkColors.outline
OkColors.grayF7 → OkColors.surfaceVariant
OkColors.grayF0 → OkColors.surfaceContainer
```

## 深色模式支持

当前系统已为深色模式做好准备，只需在 `Constants.darkThemeData` 中定义相应的深色 ColorScheme。

## 最佳实践

1. **始终使用主题颜色**：通过 `Theme.of(context).colorScheme` 访问
2. **避免硬编码颜色值**：不要直接使用 `Color(0xFF...)` 或 `Colors.xxx`
3. **保持语义化**：使用有意义的颜色名称，如 `primary`、`onSurface` 等
4. **考虑无障碍性**：确保足够的颜色对比度
5. **测试不同场景**：在不同设备和亮度下测试颜色效果

## 迁移状态

### ✅ 已完成的迁移

截至 2025年1月，所有废弃的颜色常量已成功迁移：

- `kColorPrimary` → `OkColors.primary` / `Theme.of(context).colorScheme.primary`
- `kColorSecondary` → `OkColors.secondary` / `Theme.of(context).colorScheme.secondary`
- `kColorBackground` → `OkColors.background` / `Theme.of(context).colorScheme.surface`
- `kColorTitleText` → `OkColors.onSurface`
- `kColorContentText` → `OkColors.onSurfaceVariant`
- `kColorRed` → `Theme.of(context).colorScheme.error`
- 所有灰色常量 (`grayB9`, `gray33`, `gray66`, `grayF7`) 已迁移到语义化颜色

### 📋 迁移详情

详细的迁移报告请参考：[color_migration_report.md](./color_migration_report.md)

## 工具和验证

- 使用 `color_contrast_checker.dart` 验证颜色对比度
- 使用 `flutter analyze` 检查代码质量
- 定期运行测试确保颜色系统正常工作

## 开发规范

### 新组件开发
- 优先使用 `Theme.of(context).colorScheme.*`
- 避免直接使用 `OkColors.*` 常量（除非确实需要固定颜色）
- 所有颜色组合都应通过对比度测试

### 代码审查
- 检查是否有新的硬编码颜色值
- 确保颜色使用符合语义化原则
- 验证深色模式下的显示效果
