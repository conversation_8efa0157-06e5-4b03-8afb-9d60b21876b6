import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:guests/ok_colors.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';

import 'keys.dart';

const kKeyId = 'id';
const kKeyKey = 'key';
const kKeyVal = 'val';
const kKeyData = 'data';
const kKeyName = 'name';
const kKeyPage = 'page';
const kKeyText = 'text';
const kKeyIcon = 'icon';
const kKeyPoints = 'points';
const kKeyLimit = 'limit';
const kKeyToken = 'token';
const kKeySettings = 'settings';
const kKeyPagination = 'pagination';
const kKeyKeyword = 'keyword';
const kKeyUsername = 'username';
const kKeyMessage = 'message';
const kKeyError = 'error';
const kKeyStatus = 'status';
const kKeyCreatedAt = 'created_at';
const kKeyUpdatedAt = 'updated_at';
const kKeyRememberMe = 'remember_me';
const kKeyClientCode = 'client_code';
const kKeyClientInfo = 'client_info';
const kKeyChannelCode = 'channel_code';
const kKeyInvoiceSkipped = 'invoice_skipped';
const kKeyInvoiceEnabled = 'invoice_enabled';
const kKeyInvoiceTaxId = 'invoice_tax_id';
const kKeyItemName = 'item_name';
const kKeyTaxId = 'tax_id';
const kKeyTaxType = 'tax_type';
const kDefaultItemName = '銷售商品';
const kSettingsFromDropbox =
    'https://www.dropbox.com/s/a0ak7dndolakl2r/settings.json?dl=1';
const kSettingsFromGoogle =
    'https://drive.google.com/uc?export=download&id=1IgRZsReMYSW8QEjy6TEiPwCFGJ7lUsY0';
const kSettingsUrl = kSettingsFromDropbox;
// 颜色常量已迁移到 OkColors 类，这些常量将逐步废弃
@Deprecated('Use OkColors.primary instead')
const kColorPrimary = OkColors.primary;
@Deprecated('Use OkColors.primaryLight instead')
const kColorPrimaryLight = OkColors.primaryLight;
@Deprecated('Use OkColors.primary instead')
const kColorAccent = OkColors.primary;
@Deprecated('Use OkColors.secondary instead')
const kColorSecondary = OkColors.secondary;
@Deprecated('Use OkColors.background instead')
const kColorBackground = OkColors.background;
@Deprecated('Use OkColors.onSurface instead')
const kColorTitleText = OkColors.onSurface;
@Deprecated('Use OkColors.onSurfaceVariant instead')
const kColorContentText = OkColors.onSurfaceVariant;
@Deprecated('Use OkColors.error instead')
const kColorRed = OkColors.error;
const kTaxRateNormal = 0.05;
const kTaxRateFree = 0.0;
const kDefaultPadding = 12.0;
const kButtonHeight = 46.0;
const kItemHeight = 40.0;
const kPadding = 20.0;
const kAvatarSize = kRadius * 2.0;
const kIconRadius = 37.0;
const kBottomPadding = 84.0;
const kProduct = false;
const kIconSize = kIconRadius * 2.0;
const kPrimaryGradient = const LinearGradient(
  begin: const Alignment(-1.0, 0.0),
  end: const Alignment(1.0, 0.0),
  colors: const [
    OkColors.primary,
    OkColors.secondary,
  ],
  stops: const [0.0, 1.0],
);
final kLogger = Logger(
  printer: PrettyPrinter(
    methodCount: 2, // number of method calls to be displayed
    errorMethodCount: 8, // number of method calls if stacktrace is provided
    lineLength: 120, // width of the output
    colors: true, // Colorful log messages
    printEmojis: true, // Print an emoji for each log message
    printTime: true, // Should each log print contain a timestamp
  ),
);
const kDefaultInsetPadding = EdgeInsets.symmetric(
  vertical: 24.0,
  horizontal: 40.0,
);
const kContentPadding = EdgeInsets.symmetric(
  horizontal: kPadding,
);
const kChipPadding = EdgeInsets.symmetric(
  vertical: 4.0,
  horizontal: 8.0,
);
const kRadius = 30.0;
const kRadiusCircular = const Radius.circular(kRadius);
const kBorderRadius = const BorderRadius.all(kRadiusCircular);
const kTopRadius = const BorderRadius.vertical(
  top: kRadiusCircular,
);
final kNumFormat = NumberFormat("#,##0", "en_US");
final kDateFormat = DateFormat("yyyy/MM/dd");
final kDateTimeFormat = DateFormat("yyyy/MM/dd HH:mm:ss");
// final kDateTimeFormat = DateFormat('yMd');
// final kDateTimeFormat = DateFormat.yMd().add_Hms();
final kDateTimeFormatMdHm = DateFormat("MM/dd HH:mm");
// const kAESKey = '1C020EBCDFC61E624D7EAE12F146D750'; // okshop
const kAESKey = 'C30F44D5C43C4ACA5BC83099C09E9972'; // omos
const kIv = 'Dt8lyToo17X/XkXaQvihuA==';
final kYearMonth = DateFormat("yyyyMM");
const kNumToChar = const [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
];
final kCharMax = kNumToChar.length;
const kPosBAN = '83193989';
const kCountInvoice = 8;
const kPatternInvoice = r'^\d{8}$';
final kRegExpInvoice = RegExp(kPatternInvoice);
const kLimit = 50;

class Constants {
  static const designWidth = 375.0;
  static const designHeight = 667.0;
  static const designWidthRatio = 100.0 / Constants.designWidth;
  static const designHeightRatio = 100.0 / Constants.designHeight;
  static const paddingVertical = 8.0;
  static const paddingHorizontal = 20.0;
  static const buttonHeight = kButtonHeight;
  static const paddingChip = kChipPadding;
  static const paddingContent = kContentPadding;
  static final themeData = lightThemeData;
  static final darkThemeData = ThemeData.dark().copyWith(
    brightness: Brightness.dark,
    primaryColor: OkColors.primary,
    colorScheme: const ColorScheme.dark(
      // 主要颜色
      primary: OkColors.primary,
      onPrimary: Colors.white,
      primaryContainer: Color(0xFF2D1B00),
      onPrimaryContainer: OkColors.primaryLight,

      // 次要颜色
      secondary: OkColors.secondary,
      onSecondary: Colors.white,
      secondaryContainer: Color(0xFF2D1B00),
      onSecondaryContainer: OkColors.primaryLight,

      // 表面颜色
      surface: Color(0xFF121212),
      onSurface: Color(0xFFE0E0E0),
      surfaceContainerHighest: Color(0xFF2C2C2C),
      onSurfaceVariant: Color(0xFFB0B0B0),
      surfaceContainer: Color(0xFF1E1E1E),

      // 错误颜色
      error: Color(0xFFFF6B6B),
      onError: Colors.white,
      errorContainer: Color(0xFF93000A),
      onErrorContainer: Color(0xFFFFDAD6),

      // 边框和分割线
      outline: Color(0xFF666666),
      outlineVariant: Color(0xFF404040),

      // 其他
      surfaceTint: Colors.transparent,
      shadow: Color(0x80000000),
      scrim: Color(0x80000000),
      inverseSurface: Color(0xFFE6E1E5),
      onInverseSurface: Color(0xFF313033),
      inversePrimary: OkColors.primary,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: OkColors.primary,
      elevation: 0.0,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      iconTheme: IconThemeData(
        color: Colors.white,
      ),
      titleTextStyle: TextStyle(
        fontSize: 20,
        color: Colors.white,
      ),
      centerTitle: true,
    ),
    scaffoldBackgroundColor: Color(0xFF121212),
  );
  static final lightThemeData = ThemeData.light().copyWith(
    brightness: Brightness.light,
    primaryColor: OkColors.primary,
    colorScheme: const ColorScheme.light(
      // 主要颜色
      primary: OkColors.primary,
      onPrimary: Colors.white,
      primaryContainer: OkColors.primaryLight,
      onPrimaryContainer: OkColors.primary,

      // 次要颜色
      secondary: OkColors.secondary,
      onSecondary: Colors.white,
      secondaryContainer: OkColors.primaryLight,
      onSecondaryContainer: OkColors.secondary,

      // 表面颜色
      surface: OkColors.surface,
      onSurface: OkColors.onSurface,
      surfaceContainerHighest: OkColors.surfaceVariant,
      onSurfaceVariant: OkColors.onSurfaceVariant,
      surfaceContainer: OkColors.surfaceContainer,

      // 错误颜色
      error: OkColors.error,
      onError: Colors.white,
      errorContainer: Color(0xFFFFEDEA),
      onErrorContainer: OkColors.error,

      // 边框和分割线
      outline: OkColors.outline,
      outlineVariant: Color(0xFFE0E0E0),

      // 其他
      surfaceTint: Colors.transparent,
      shadow: Color(0x29000000),
      scrim: Color(0x80000000),
      inverseSurface: Color(0xFF2D3135),
      onInverseSurface: Color(0xFFF1F0F4),
      inversePrimary: Color(0xFFFFB77C),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: OkColors.primary,
      elevation: 0.0,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      iconTheme: IconThemeData(
        color: Colors.white,
      ),
      titleTextStyle: TextStyle(
        fontSize: 20,
        color: Colors.white,
      ),
      centerTitle: true,
    ),
    actionIconTheme: ActionIconThemeData(
      backButtonIconBuilder: (context) => const SizedBox(
        width: 40,
        height: 40,
        child: DecoratedBox(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Color(0x80ffffff),
          ),
          child: Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
        ),
      ),
    ),
    scaffoldBackgroundColor: OkColors.primary,
    switchTheme: SwitchThemeData(
      thumbColor:
          WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
        if (states.contains(WidgetState.disabled)) {
          return Colors.grey.withOpacity(0.5);
        }
        if (states.contains(WidgetState.selected)) {
          return OkColors.primary;
        }
        return Colors.grey;
      }),
      trackColor:
          WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
        if (states.contains(WidgetState.disabled)) {
          return Colors.grey.withOpacity(0.3);
        }
        if (states.contains(WidgetState.selected)) {
          return OkColors.primary.withOpacity(0.5);
        }
        return Colors.grey.withOpacity(0.3);
      }),
      overlayColor:
          WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
        if (states.contains(WidgetState.selected)) {
          return OkColors.primary.withOpacity(0.1);
        }
        if (states.contains(WidgetState.hovered)) {
          return Colors.grey.withOpacity(0.1);
        }
        return null;
      }),
    ),
    // bottomNavigationBarTheme: const BottomNavigationBarThemeData(
    //   backgroundColor: OkColors.background,
    //   selectedItemColor: OkColors.primary,
    //   unselectedItemColor: OkColors.onSurfaceVariant,
    //   showUnselectedLabels: true,
    //   showSelectedLabels: true,
    // ),
    indicatorColor: OkColors.primary,
    progressIndicatorTheme: ProgressIndicatorThemeData(
      color: OkColors.primary,
    ),
    textSelectionTheme: const TextSelectionThemeData(
      cursorColor: OkColors.primary,
      selectionColor: OkColors.primary,
      selectionHandleColor: OkColors.primary,
    ),
    inputDecorationTheme: const InputDecorationTheme(
      fillColor: Colors.white,
      filled: true,
      // border: UnderlineInputBorder(),
      contentPadding: EdgeInsets.symmetric(
        vertical: 8.0,
      ),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: OkColors.outline),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: OkColors.primary),
      ),
      hintStyle: TextStyle(
        fontSize: 16.0,
        color: OkColors.onSurfaceVariant,
      ),
      labelStyle: TextStyle(
        fontSize: 16.0,
        color: OkColors.onSurface,
      ),
      prefixStyle: TextStyle(
        fontSize: 16.0,
        color: OkColors.onSurfaceVariant,
      ),
      suffixStyle: TextStyle(
        fontSize: 16.0,
        color: OkColors.onSurfaceVariant,
      ),
    ),
    checkboxTheme: CheckboxThemeData(
      fillColor:
          WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
        if (states.contains(WidgetState.disabled)) {
          return OkColors.outline;
        }
        if (states.contains(WidgetState.selected)) {
          return OkColors.primary;
        }
        return null;
      }),
      checkColor: WidgetStateProperty.all(Colors.white),
      overlayColor: WidgetStateProperty.all(OkColors.primary.withOpacity(0.1)),
    ),
    tabBarTheme: const TabBarTheme(
      indicatorSize: TabBarIndicatorSize.tab,
      indicator: BoxDecoration(
        color: OkColors.primary,
      ),
      labelColor: Colors.white,
      indicatorColor: OkColors.primary,
      labelPadding: EdgeInsets.zero,
      labelStyle: TextStyle(
        fontSize: 16,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: 16,
      ),
    ),
    // elevatedButtonTheme: ElevatedButtonThemeData(
    //   style: ElevatedButton.styleFrom(
    //     shape: const StadiumBorder(),
    //     minimumSize: Size.zero,
    //   ),
    // ),
    // outlinedButtonTheme: OutlinedButtonThemeData(
    //   style: OutlinedButton.styleFrom(
    //     side: BorderSide(color: OkColors.primary),
    //     shape: const StadiumBorder(),
    //     minimumSize: Size.zero,
    //   ),
    // ),
    // dialogTheme: const DialogTheme(
    //   backgroundColor: Colors.white,
    //   shape: RoundedRectangleBorder(
    //     borderRadius: kBorderRadius,
    //   ),
    // ),
    // floatingActionButtonTheme: const FloatingActionButtonThemeData(
    //   backgroundColor: OkColors.primary,
    // ),
    // iconTheme: const IconThemeData(
    //   color: OkColors.primary,
    // ),
    listTileTheme: const ListTileThemeData(
      tileColor: OkColors.surface,
      contentPadding: kContentPadding,
      leadingAndTrailingTextStyle: TextStyle(
        fontSize: 16,
        color: OkColors.onSurface,
      ),
      titleTextStyle: TextStyle(
        fontSize: 16,
        color: OkColors.onSurface,
      ),
    ),
  );
}

const boxes = [
  Keys.BoxOrder,
  Keys.BoxOrderDetail,
  Keys.BoxOrderInvoice,
  Keys.BoxAccount,
];
